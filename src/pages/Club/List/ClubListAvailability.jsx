import Availability from "Components/Shared/Availability";
import { useState, useEffect, useContext } from "react";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext } from "Context/Global";
import LoadingSpinner from "Components/LoadingSpinner";
import { useClub } from "Context/Club";

let sdk = new MkdSDK();

export default function ClubListAvailability() {
  const [clubAvailability, setClubAvailability] = useState(null);
  const { club, sports } = useClub();
  const { state: globalState, dispatch: globalDispatch } =
    useContext(GlobalContext);
  const [isLoading, setIsLoading] = useState(false);

  async function fetchClubAvailability(filter = {}) {
    try {
      setIsLoading(true);
      let queryParams = new URLSearchParams();

      if (filter.date) {
        queryParams.append("date", filter.date);
      }
      if (filter.time) {
        queryParams.append("time", filter.time);
      }
      if (filter.sport_id) {
        queryParams.append("sport_id", filter.sport_id);
      }
      const queryString = queryParams.toString();
      const endpoint = `/v3/api/custom/courtmatchup/club/reservations/availability${
        queryString ? `?${queryString}` : ""
      }`;
      const response = await sdk.callRawAPI(endpoint, {}, "GET");
      setClubAvailability(response);
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    if (club?.id) {
      fetchClubAvailability();
    }
  }, [club?.id]);

  return (
    <div>
      {isLoading && <LoadingSpinner />}
      <Availability
        clubAvailability={clubAvailability}
        fetchClubAvailability={fetchClubAvailability}
        sports={sports}
        club={club}
      />
    </div>
  );
}
