import prettier from "prettier/standalone";
import parserBabel from "prettier/parser-babel";
import metadataJSON from "Utils/metadata.json";
import { loadStripe } from "@stripe/stripe-js";
import moment from "moment";

export function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

export const getNonNullValue = (value) => {
  if (value != "") {
    return value;
  } else {
    return undefined;
  }
};

export function filterEmptyFields(object) {
  Object.keys(object).forEach((key) => {
    if (empty(object[key])) {
      delete object[key];
    }
  });
  return object;
}

export function empty(value) {
  return (
    value === "" ||
    value === null ||
    value === undefined ||
    value === "undefined"
  );
}

export const isImage = (file) => {
  const validImageTypes = ["image/gif", "image/jpeg", "image/jpg", "image/png"];
  if (validImageTypes.includes(file.file.type)) return true;
  return false;
};

export const isVideo = (file) => {
  const validVideoTypes = ["video/webm", "video/mp4"];
  if (validVideoTypes.includes(file.file.type)) return true;
  return false;
};

export const isPdf = (file) => {
  const validVideoTypes = ["application/pdf"];
  if (validVideoTypes.includes(file.file.type)) return true;
  return false;
};

export const randomString = (length) => {
  let result = "";
  let characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};

export const generateUUID = () => {
  const s4 = () => {
    return Math.floor((1 + Math.random()) * 0x10000)
      .toString(16)
      .substring(1);
  };

  return (
    s4() +
    s4() +
    "-" +
    s4() +
    "-" +
    s4() +
    "-" +
    s4() +
    "-" +
    s4() +
    s4() +
    s4()
  );
};

export const capitalize = (string) => {
  const removedSpecialCharacters = string.replace(/[^a-zA-Z0-9]/g, " ");

  const splitWords = removedSpecialCharacters.split(" ").filter(Boolean);

  const capitalized = splitWords.map(
    (dt) => `${dt[0].toUpperCase()}${dt.substring(1)} `
  );

  return capitalized.join(" ");
};

export const dateHandle = (date) => {
  const newDate = date
    ? new Date(date).toISOString().split("T")[0]
    : new Date().toISOString().split("T")[0];
  return newDate;
};

export const ghrapDate = (date) => {
  const newDate = new Date(date);
  var mS = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "June",
    "July",
    "Aug",
    "Sept",
    "Oct",
    "Nov",
    "Dec",
  ];
  console.log(newDate.getDate(), mS[newDate.getMonth()]);

  return `${newDate.getDate()} ${mS[newDate.getMonth()]} `;
};

export const formatCode = function (code) {
  return prettier.format(code, {
    parser: "babel",
    plugins: [parserBabel],
    singleQuote: true,
    trailingComma: "es5",
    jsxSingleQuote: true,
    printWidth: 80,
    tabWidth: 2,
  });
};

export const slugify = (str) =>
  str
    .toLowerCase()
    .trim()
    .replace(/[^ws-]/g, "")
    .replace(/[s_-]+/g, "-")
    .replace(/^-+|-+$/g, "");

/**
 * @typedef {Object} StringCaserOptions
 * @property {"space" | String} separator - define what separates each word, undefined returns no separation - passing "space" separates the words by a space
 * @property {"uppercase" | "lowercase" | "capitalize" | "camelCase" | "PascalCase"} casetype - text case type, uppercase, lowercase of capitalized | default is lowercase
 */
/**
 *
 * @param {String} string - text to convert
 * @param {StringCaserOptions} options - options
 * @returns
 */
export const StringCaser = (string, options) => {
  if (!string) return null;
  if (typeof string !== "string") return null;
  const removedSpecialCharacters = string.replace(/[^a-zA-Z0-9]/g, " ");
  let casedText = [];
  const splitWords = removedSpecialCharacters.split(" ").filter(Boolean);

  if (options?.casetype === "capitalize") {
    casedText = splitWords.map(
      (/** @type {string} */ dt) => `${dt[0].toUpperCase()}${dt.substring(1)} `
    );
  }
  if (options?.casetype === "uppercase") {
    casedText = splitWords.map((/** @type {string} */ dt) => dt.toUpperCase());
  }
  if (options?.casetype === "lowercase") {
    casedText = splitWords.map((/** @type {string} */ dt) => dt.toLowerCase());
  }
  if (options?.casetype === "camelCase") {
    casedText = splitWords.map((/** @type {string} */ dt, index) =>
      index === 0
        ? dt.toLowerCase()
        : `${dt[0].toUpperCase()}${dt.substring(1)} `
    );
  }
  if (options?.casetype === "PascalCase") {
    casedText = splitWords.map(
      (/** @type {string} */ dt) => `${dt[0].toUpperCase()}${dt.substring(1)}`
    );
  }

  if (options?.separator) {
    if (options?.separator === "space") {
      return casedText.join(" ");
    } else {
      return casedText.join(options?.separator);
    }
  } else {
    return casedText.join("");
  }
};

export const testColumns = [
  {
    header: "Action",
    show: true,
    accessor: "",
  },

  {
    header: "Id",
    accessor: "id",
    show: false,
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "User Id",
    accessor: "user_id",
    show: false,
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "First Name",
    accessor: "first_name",
    show: true,
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Last Name",
    accessor: "last_name",
    show: true,
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Email",
    accessor: "email",
    show: true,
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Role",
    accessor: "role",
    show: true,
    isSorted: false,
    isSortedDesc: false,
    mappingExist: true,
    mappings: {
      admin: "Admin",
      employee: "Employee",
    },
  },
  {
    header: "Photo",
    accessor: "photo",
    show: true,
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Phone",
    accessor: "phone",
    show: true,
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Status",
    accessor: "status",
    show: false,
    isSorted: false,
    isSortedDesc: false,
    mappingExist: true,
    mappings: { 0: "pending", 1: "approved" },
  },
  {
    header: "Type",
    accessor: "type",
    show: false,
    isSorted: false,
    isSortedDesc: false,
    mappingExist: true,
    mappings: {
      0: "normal",
      1: "facebook",
      2: "google",
    },
  },
  {
    header: "Verify",
    accessor: "verify",
    show: false,
    isSorted: false,
    isSortedDesc: false,
    mappingExist: true,
    mappings: {
      0: "not verified",
      1: "verified",
    },
  },

  {
    header: "Create At",
    accessor: "create_at",
    show: false,
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Update At",
    accessor: "update_at",
    show: false,
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
];

export const exceptionalHtmlElements = [
  "audio",
  "image",
  "img",
  "heading",
  "video",
  "h1",
  "h2",
  "h3",
  "h4",
  "h5",
  "h6",
  "hr",
  "br",
  "a",
  "p",
  "link",
  "span",
];
export const excludedHtmlElements = [
  "abbr",
  "address",
  "area",
  "article",
  "aside",
  "b",
  "base",
  "bdi",
  "bdo",
  "blockquote",
  "body",
  "button",
  "canvas",
  "caption",
  "cite",
  "code",
  "col",
  "colgroup",
  "data",
  "datalist",
  "dd",
  "del",
  "details",
  "dfn",
  "dialog",
  "div",
  "dl",
  "dt",
  "em",
  "embed",
  "fieldset",
  "figcaption",
  "figure",
  "footer",
  "form",
  "head",
  "header",
  "hgroup",
  "html",
  "i",
  "iframe",
  "input",
  "ins",
  "kbd",
  "label",
  "legend",
  "li",
  // "link",
  "main",
  "map",
  "mark",
  "meta",
  "meter",
  "nav",
  "noscript",
  "object",
  "ol",
  "optgroup",
  "option",
  "output",
  // "p",
  "param",
  "picture",
  "pre",
  "progress",
  "q",
  "rb",
  "rp",
  "rt",
  "rtc",
  "ruby",
  "s",
  "samp",
  "script",
  "section",
  "select",
  "slot",
  "small",
  "source",
  "strong",
  "style",
  "sub",
  "summary",
  "sup",
  "table",
  "tbody",
  "td",
  "template",
  "textarea",
  "tfoot",
  "th",
  "thead",
  "time",
  "title",
  "tr",
  "track",
  "u",
  "ul",
  "var",
  "wbr",
];

export const locationOptions = [
  { id: 1, label: "Indoor" },
  { id: 2, label: "Outdoor" },
];

export const ntrpOptions = [
  2.0, 2.5, 3.0, 3.5, 4.0, 4.5, 5.0, 5.5, 6.0, 6.5, 7.0, 7.5, 8.0,
];

export const eventTypeOptions = [
  {
    label: "court",
    value: 1,
  },
  {
    label: "clinic",
    value: 2,
  },
  // {
  //   label: "coach",
  //   value: 3,
  // },
  {
    label: "lesson",
    value: 4,
  },
  {
    label: "custom",
    value: 5,
  },
];

export const reservationTypes = {
  court: 1,
  clinic: 2,
  coach: 3,
  lesson: 4,
  custom: 5,
};

export function convertTimeSlots(inputData) {
  return inputData.flatMap((entry) =>
    entry.timeslots.map((timeSlot) => ({
      day: entry.day,
      time: timeSlot,
    }))
  );
}

export const timeslots = [
  "00:00",
  "01:00",
  "02:00",
  "03:00",
  "04:00",
  "05:00",
  "06:00",
  "07:00",
  "08:00",
  "09:00",
  "10:00",
  "11:00",
  "12:00",
  "13:00",
  "14:00",
  "15:00",
  "16:00",
  "17:00",
  "18:00",
  "19:00",
  "20:00",
  "21:00",
  "22:00",
  "23:00",
];

export const calculateAgeGroup = (dateOfBirth) => {
  if (!dateOfBirth) return "N/A";

  const [day, month, year] = dateOfBirth.split("/").map(Number);
  const birthDate = new Date(year, month - 1, day);
  const today = new Date();

  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }

  if (age < 18) return "Minor";
  if (age >= 18 && age < 60) return "Adult";
  return "Senior";
};

export const convertTo12Hour = (time24) => {
  if (!time24) return "";
  const [hours24, minutes] = time24.split(":");
  const period = hours24 >= 12 ? "PM" : "AM";
  let hours12 = hours24 % 12;
  hours12 = hours12 || 12; // Convert '0' to '12'
  return `${hours12.toString().padStart(2, "0")}:${minutes} ${period}`;
};

export const convertTo24Hour = (time12) => {
  if (!time12) return "";
  const [time, period] = time12.split(" ");
  let [hours, minutes] = time.split(":");
  hours = parseInt(hours);

  if (period === "PM" && hours !== 12) hours += 12;
  if (period === "AM" && hours === 12) hours = 0;

  return `${hours.toString().padStart(2, "0")}:${minutes}`;
};

export const generateSlug = (name) => {
  return name
    .toLowerCase() // Convert to lowercase
    .trim() // Remove leading and trailing whitespace
    .replace(/[^\w\s-]/g, "") // Remove special characters
    .replace(/\s+/g, "-") // Replace spaces with hyphens
    .replace(/-+/g, "-"); // Replace multiple hyphens with single hyphen
};

export const updateFavicon = (iconUrl) => {
  // Update main favicon
  const favicon = document.getElementById("favicon");
  if (favicon) favicon.href = iconUrl;

  // Update Apple touch icon
  const appleIcon = document.getElementById("apple-touch-icon");
  if (appleIcon) appleIcon.href = iconUrl;
};

export const generateTimeSlots = () => {
  const slots = [];
  for (let hour = 11; hour <= 24; hour++) {
    for (let minute of ["00", "30"]) {
      // Handle midnight (24:00) as 12:00 AM
      const actualHour = hour === 24 ? 0 : hour;

      // 24-hour format for internal use
      const time24 = `${actualHour.toString().padStart(2, "0")}:${minute}`;

      // 12-hour format for display
      const period = actualHour >= 12 ? "PM" : "AM";
      const hour12 =
        actualHour === 0 ? 12 : actualHour > 12 ? actualHour - 12 : actualHour;
      const time12 = `${hour12}:${minute} ${period}`;

      slots.push({ time24, time12 });
    }
  }
  return slots;
};

export const getFilteredUntilTimeOptions = (fromTime) => {
  if (!fromTime) return timeOptions;
  const fromTimeIndex = timeOptions.indexOf(fromTime);
  return timeOptions.slice(fromTimeIndex + 1);
};
export const timeOptions = [
  "00:00:00",
  "01:00:00",
  "02:00:00",
  "03:00:00",
  "04:00:00",
  "05:00:00",
  "06:00:00",
  "07:00:00",
  "08:00:00",
  "09:00:00",
  "10:00:00",
  "11:00:00",
  "12:00:00",
  "13:00:00",
  "14:00:00",
  "15:00:00",
  "16:00:00",
  "17:00:00",
  "18:00:00",
  "19:00:00",
  "20:00:00",
  "21:00:00",
  "22:00:00",
  "23:00:00",
];
export const RoleMap = {
  member: "user",
  admin: "admin",
  club: "club",
  coach: "coach",
};

export const updatedRole = (role, location) => {
  const determinedRole = RoleMap[role];
  if (!determinedRole) {
    if (location.pathname.includes("admin")) {
      return "admin";
    }
    if (location.pathname.includes("user")) {
      return "user";
    }
    if (location.pathname.includes("club")) {
      return "club";
    }

    return "user";
  }

  return determinedRole;
};

export const updateBrowserTab = ({
  title,
  path,
  clubName,
  favicon,
  description,
}) => {
  console.log("updateBrowserTab", {
    title,
    path,
    clubName,
    favicon,
    description,
  });
  // Update the title
  const defaultTitle = clubName || "Courtmatchup";
  const metadata = metadataJSON[path ?? "/"];
  const finalTitle = description
    ? `${description} | ${defaultTitle}`
    : `${metadata?.description} | ${defaultTitle}`;
  document.title = finalTitle;

  // Update favicon if provided
  if (favicon) {
    const faviconElement = document.getElementById("favicon");
    const appleIconElement = document.getElementById("apple-touch-icon");

    if (faviconElement) faviconElement.href = favicon;
    if (appleIconElement) appleIconElement.href = favicon;
  }
};

export const calculateServiceFee = (clubFeeSettings, baseAmount = 0) => {
  try {
    const feeSettings = JSON.parse(clubFeeSettings);
    if (!feeSettings || !feeSettings.length) return 0;

    const feeSetting = feeSettings[0];

    if (feeSetting.fee_type === "tech") {
      // Calculate percentage fee based on the base amount
      const percentageFee = baseAmount * 0.01; // 1% of base amount
      // Add fixed fee of $0.49
      const fixedFee = 0.49;
      // Round to 2 decimal places
      return Number((percentageFee + fixedFee).toFixed(2));
    } else if (feeSetting.fee_type === "monthly") {
      return 0;
    }

    return 0;
  } catch (error) {
    console.error("Error parsing fee settings:", error);
    return 0;
  }
};

export const groupConsecutiveTimeSlots = (timeSlots) => {
  if (!timeSlots || timeSlots.length === 0) return [];

  // Sort time slots
  const sortedSlots = [...timeSlots].sort((a, b) => {
    const [hoursA] = a.split(":");
    const [hoursB] = b.split(":");
    return parseInt(hoursA) - parseInt(hoursB);
  });

  const ranges = [];
  let currentRange = {
    start: sortedSlots[0],
    end: sortedSlots[0],
  };

  for (let i = 1; i < sortedSlots.length; i++) {
    const currentTime = sortedSlots[i];
    const [currentHour] = currentTime.split(":");
    const [prevHour] = sortedSlots[i - 1].split(":");

    // Check if times are consecutive (difference of 1 hour)
    if (parseInt(currentHour) - parseInt(prevHour) === 1) {
      currentRange.end = currentTime;
    } else {
      // If not consecutive, push current range and start a new one
      ranges.push(currentRange);
      currentRange = {
        start: currentTime,
        end: currentTime,
      };
    }
  }

  // Push the last range
  ranges.push(currentRange);

  return ranges;
};

export const BOOKING_STATUSES = {
  SUCCESS: 1,
  PENDING: 0,
  FAIL: 2,
  CANCELLED: 3,
};

export const stripePromise = loadStripe(
  "pk_test_51Ll5ukBgOlWo0lDUrBhA2W7EX2MwUH9AR5Y3KQoujf7PTQagZAJylWP1UOFbtH4UwxoufZbInwehQppWAq53kmNC00UIKSmebO"
);
export const formatTimeToSQL = (timeStr) => {
  // Convert "11:00 AM" to "11:00:00" format
  const [time, period] = timeStr.split(" ");
  let [hours, minutes] = time.split(":");
  hours = parseInt(hours);

  // Convert to 24-hour format
  if (period === "PM" && hours < 12) {
    hours += 12;
  }
  if (period === "AM" && hours === 12) {
    hours = 0;
  }
  // Pad with leading zeros and add seconds
  return `${hours.toString().padStart(2, "0")}:${minutes}:00`;
};

export const getTimeRange = (selectedTimes) => {
  if (!selectedTimes?.length) {
    return {
      start_time: null,
      end_time: null,
      duration: 0,
    };
  }

  const start_time = formatTimeToSQL(
    selectedTimes.reduce(
      (earliest, current) =>
        current.from < earliest ? current.from : earliest,
      selectedTimes[0].from
    )
  );

  const end_time = formatTimeToSQL(
    selectedTimes.reduce(
      (latest, current) => (current.until > latest ? current.until : latest),
      selectedTimes[0].until
    )
  );

  // Calculate duration in hours
  const startParts = start_time.split(":").map(Number);
  const endParts = end_time.split(":").map(Number);
  const startMinutes = startParts[0] * 60 + startParts[1];
  const endMinutes = endParts[0] * 60 + endParts[1];
  const duration = (endMinutes - startMinutes) / 60;

  return {
    start_time,
    end_time,
    duration,
  };
};

export const getTimeRangeFromSlots = (timeSlots) => {
  if (!timeSlots?.length) {
    return {
      start_time: null,
      end_time: null,
    };
  }

  // Find earliest from time
  const start_time = formatTimeToSQL(
    timeSlots.reduce(
      (earliest, current) =>
        current.from < earliest ? current.from : earliest,
      timeSlots[0].from
    )
  );

  // Find latest until time
  const end_time = formatTimeToSQL(
    timeSlots.reduce(
      (latest, current) => (current.until > latest ? current.until : latest),
      timeSlots[0].until
    )
  );

  return {
    start_time,
    end_time,
  };
};

export const calculateReservationTimeLeft = (bookingDate) => {
  if (!bookingDate) return "0min";

  const now = new Date();
  const bookingDateTime = new Date(bookingDate); // Convert string to Date
  const expiryTime = new Date(bookingDateTime.getTime() + 15 * 60 * 1000); // Add 15 minutes
  const timeDiff = expiryTime.getTime() - now.getTime();

  if (timeDiff <= 0) {
    return "0min";
  }

  const minutes = Math.floor(timeDiff / (1000 * 60));
  return `${minutes}min`;
};

// Check if reservation can be canceled based on sport settings and timing
export const canCancelReservation = (reservation, clubSports) => {
  if (!reservation || !clubSports) return false;

  // Find the sport for this reservation
  const sport = clubSports.find((sport) => sport.id === reservation.sport_id);
  if (!sport) return false;

  // Check if sport allows cancellation
  if (sport.allow_cancel_reservation !== 1) return false;

  // Check if we're within the allowed cancellation window
  const now = new Date();
  const reservationDateTime = new Date(
    `${reservation.booking_date} ${reservation.start_time}`
  );
  const hoursUntilReservation =
    (reservationDateTime.getTime() - now.getTime()) / (1000 * 60 * 60);

  // Must be at least cancel_hours_before hours before the reservation
  return hoursUntilReservation >= sport.cancel_hours_before;
};

/**
 * Calculates the total fees for a lesson or coaching session
 * @param {Object} params - Parameters for fee calculation
 * @param {number} params.hourlyRate - Coach's hourly rate
 * @param {number} params.hours - Number of hours booked
 * @param {number} params.playerCount - Number of players
 * @param {Object} params.feeSettings - Club fee settings for service fee calculation
 * @returns {Object} Object containing coachFee, serviceFee and total
 */
export const calculateCoachTotalFees = ({
  hourlyRate,
  hours,
  playerCount,
  feeSettings,
}) => {
  // Calculate base coach fee per hour per player
  const baseCoachFee = (hourlyRate || 0) * (hours || 0) * (playerCount || 0);

  // Calculate service fee based on the base coach fee
  const serviceFee = calculateServiceFee(feeSettings, baseCoachFee);

  // Return fee breakdown
  return {
    coachFee: baseCoachFee,
    serviceFee: serviceFee,
    total: baseCoachFee + serviceFee,
  };
};

/**
 * Calculates the total fees for a clinic booking
 * @param {Object} params - Parameters for fee calculation
 * @param {number} params.costPerHead - Cost per player for the clinic
 * @param {number} params.playerCount - Number of players
 * @param {Object} params.feeSettings - Club fee settings for service fee calculation
 * @returns {Object} Object containing clinicFee, serviceFee and total
 */
export const calculateClinicFees = ({
  costPerHead,
  playerCount,
  feeSettings,
}) => {
  // Calculate base clinic fee
  const clinicFee = (costPerHead || 0) * (playerCount || 0);

  // Calculate service fee based on the clinic fee
  const serviceFee = calculateServiceFee(feeSettings, clinicFee);

  // Return fee breakdown
  return {
    clinicFee,
    serviceFee,
    total: clinicFee + serviceFee,
  };
};

export const generateTimeOptions = () => {
  const options = [];
  for (let hour = 0; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const hour24 = hour.toString().padStart(2, "0");
      const min = minute.toString().padStart(2, "0");
      const value = `${hour24}:${min}`;

      // Format for AM/PM display
      let hour12 = hour % 12;
      if (hour12 === 0) hour12 = 12;
      const ampm = hour < 12 ? "AM" : "PM";
      const label = `${hour12}:${min} ${ampm}`;

      options.push({ value, label });
    }
  }
  return options;
};

export const actionLogTypes = {
  CREATE: 1,
  UPDATE: 2,
  DELETE: 3,
};

export const activityLogTypes = {
  court_reservation: "COURT_RESERVATION",
  group: "GROUP",
  clinic: "CLINIC_RESERVATION",
  lesson: "LESSON_RESERVATION",
  custom: "CUSTOM_RESERVATION",
  find_a_buddy: "FIND_A_BUDDY",
  club_ui: "CLUB_UI",
  faq: "FAQ",
  court_management: "COURT_MANAGEMENT",
  user_management: "USER_MANAGEMENT",
  coach_management: "COACH_MANAGEMENT",
  staff_management: "STAFF_MANAGEMENT",
  invoice: "INVOICE",
};

export const logActivity = async (
  sdk,
  { user_id, activity_type, action_type, data, club_id, description }
) => {
  try {
    sdk.setTable("activity_logs");
    await sdk.callRestAPI(
      {
        user_id,
        activity_type,
        action_type,
        data: typeof data === "string" ? data : JSON.stringify(data),
        club_id,
        description,
      },
      "POST"
    );
  } catch (error) {
    console.error("Error logging activity:", error);
    throw error;
  }
};

export const getCourtPrice = ({
  pricing,
  sportId,
  type,
  subType,
  selectedTime,
  duration,
}) => {
  if (!pricing || !sportId || !type || !subType || !selectedTime) return 0;

  // Find specific pricing for the selected sport, type, and sub-type
  const specificPricing = pricing.find(
    (p) =>
      p.sport_id === sportId &&
      p.type === type &&
      p.surface_id === subType &&
      p.is_general === 0
  );

  // Find general pricing for the selected sport
  const sportGeneralPricing = pricing.find(
    (p) => p.sport_id === sportId && p.is_general === 1
  );

  // Find overall general pricing
  const overallGeneralPricing = pricing.find(
    (p) => p.sport_id === null && p.is_general === 1
  );

  let baseAmount = 0;

  if (specificPricing) {
    // If there's specific pricing with time slots
    const parsedPriceByHours = JSON.parse(specificPricing.price_by_hours);

    // Convert times to comparable format
    const selectedStartTime = moment(selectedTime.from, "HH:mm").format(
      "HH:mm"
    );
    const selectedEndTime = moment(selectedTime.until, "HH:mm").format("HH:mm");

    // Find matching time slot
    const matchingTimeSlot = parsedPriceByHours.find((slot) => {
      const slotStartTime = moment(slot.start_time, "HH:mm").format("HH:mm");
      const slotEndTime = moment(slot.end_time, "HH:mm").format("HH:mm");
      return (
        selectedStartTime >= slotStartTime && selectedEndTime <= slotEndTime
      );
    });

    if (matchingTimeSlot) {
      baseAmount = parseFloat(matchingTimeSlot.rate) * duration;
    } else {
      // If no matching time slot, use the first rate and multiply by duration
      baseAmount = parseFloat(parsedPriceByHours[0].rate) * duration;
    }
  } else if (sportGeneralPricing) {
    baseAmount = parseFloat(sportGeneralPricing.general_rate) * duration;
  } else if (overallGeneralPricing) {
    baseAmount = parseFloat(overallGeneralPricing.general_rate) * duration;
  }

  return baseAmount;
};
