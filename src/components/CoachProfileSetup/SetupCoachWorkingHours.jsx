import { useState, useRef } from "react";
import TimeSlotGrid from "../TimeSlotGrid";
import { useForm } from "react-hook-form";
import { InteractiveButton } from "Components/InteractiveButton";

const days = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday",
];

export default function SetupCoachWorkingHours({
  onNext,
  isSubmitting,
  selectedTimeSlot,
  setSelectedTimeSlot,
  originalAvailability,
  setOriginalAvailability,
  setValue: parentSetValue,
  defaultValues,
}) {
  const [selectedSlots, setSelectedSlots] = useState({});
  // console.log(defaultValues);
  const {
    handleSubmit,
    formState: { errors },
  } = useForm();

  // Update the isSelected check to be more flexible
  const isSelected = (time, day) => {
    const daySlot = selectedTimeSlot?.find(
      (daySlot) => daySlot.day === day.toLowerCase()
    );
    if (!daySlot) return false;

    const timeWithoutSeconds = time.replace(":00", "");
    return daySlot.timeslots.some(
      (t) => t === time || t.replace(":00", "") === timeWithoutSeconds
    );
  };

  const handleTimeSelect = (time, day) => {
    setSelectedTimeSlot((prev) => {
      return prev.map((daySlot) => {
        if (daySlot.day === day.toLowerCase()) {
          const timeWithoutSeconds = time.replace(":00", "");
          const timeExists = daySlot.timeslots.some(
            (t) => t === time || t === timeWithoutSeconds
          );
          if (!timeExists) {
            return {
              ...daySlot,
              timeslots: [...daySlot.timeslots, time].sort(),
            };
          }
        }
        return daySlot;
      });
    });
  };

  const handleDeleteTime = (time, day) => {
    setSelectedTimeSlot((prev) => {
      return prev.map((daySlot) => {
        if (daySlot.day === day.toLowerCase()) {
          return {
            ...daySlot,
            timeslots: daySlot.timeslots.filter(
              (t) => t !== time && t !== time.replace(":00", "")
            ),
          };
        }
        return daySlot;
      });
    });
  };

  const formatSelectedTimes = () => {
    return selectedTimeSlot.filter((daySlot) => daySlot.timeslots.length > 0);
  };

  const onSave = async () => {
    try {
      const formattedTimes = formatSelectedTimes();
      parentSetValue("availability", formattedTimes);
      await onNext();
    } catch (error) {
      console.error("Error saving working hours:", error);
    }
  };
  const onSkip = async () => {
    parentSetValue("availability", []);
    await onNext();
  };
  return (
    <div className="mx-auto w-full max-w-7xl px-4">
      <h1 className="mb-8 text-center text-2xl font-semibold">
        Set your working hours
      </h1>

      <div>
        <TimeSlotGrid
          days={days}
          isSelected={isSelected}
          handleTimeSelect={handleTimeSelect}
          handleDeleteTime={handleDeleteTime}
        />

        {/* {Object.keys(selectedSlots).length === 0 && (
          <p className="mt-2 text-center text-sm text-red-500">
            Please select at least one time slot
          </p>
        )} */}

        <div className="mt-8 flex justify-center gap-4">
          <InteractiveButton
            type="button"
            onClick={onSkip}
            className="rounded-lg border border-gray-300 px-6 py-2 hover:bg-gray-50"
          >
            Skip
          </InteractiveButton>
          <InteractiveButton
            type="submit"
            onClick={onSave}
            loading={isSubmitting}
            disabled={formatSelectedTimes().length === 0}
            className="rounded-lg bg-primaryGreen px-6 py-2 text-white hover:bg-primaryGreen/80 disabled:opacity-50"
          >
            Continue
          </InteractiveButton>
        </div>
      </div>
    </div>
  );
}
