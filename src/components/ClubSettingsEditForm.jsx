import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { generateTimeOptions } from "Utils/utils";
import Select from "react-select";
import ConfirmEventsDeleteModal from "./Modals/ConfirmEventsDeleteModal";
import RightSideModal from "./RightSideModal";
import MkdSDK from "Utils/MkdSDK";

const ClubSettingsEditForm = React.forwardRef(
  (
    {
      onSubmit = () => {},
      initialData = {},
      club = {},
      isOpen = false,
      onClose = () => {},
      title = "Edit club settings",
      onPrimaryAction = () => {},
      submitting = false,
    },
    ref
  ) => {
    const sdk = new MkdSDK();
    const { handleSubmit } = useForm();

    // Helper function to round time to nearest 30-minute interval
    const roundToNearest30Minutes = (timeString) => {
      if (!timeString) return timeString;

      const [hours, minutes] = timeString.split(":").map(Number);
      const totalMinutes = hours * 60 + minutes;

      // Round to nearest 30-minute interval
      const roundedMinutes = Math.round(totalMinutes / 30) * 30;
      const roundedHours = Math.floor(roundedMinutes / 60) % 24;
      const remainingMinutes = roundedMinutes % 60;

      return `${roundedHours.toString().padStart(2, "0")}:${remainingMinutes
        .toString()
        .padStart(2, "0")}:00`;
    };

    const parsedTimes = club?.times ? JSON.parse(club.times) : [];
    const [timeSlots, setTimeSlots] = useState(() => {
      if (parsedTimes.length > 0) {
        console.log("Original parsed times:", parsedTimes);
        // Round existing times to nearest 30-minute intervals
        const roundedTimes = parsedTimes.map((timeSlot) => ({
          from: roundToNearest30Minutes(timeSlot.from),
          until: roundToNearest30Minutes(timeSlot.until),
        }));
        console.log("Rounded times to 30-minute intervals:", roundedTimes);
        return roundedTimes;
      }
      return [{ from: "", until: "" }];
    });
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [affectedEventCount, setAffectedEventCount] = useState(0);
    const [affectedReservations, setAffectedReservations] = useState([]);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isCheckingAffectedEvents, setIsCheckingAffectedEvents] =
      useState(false);
    const [pendingData, setPendingData] = useState(null);

    // Debug time slots whenever they change
    React.useEffect(() => {
      console.log("Current time slots:", timeSlots);
    }, [timeSlots]);

    const handleTimeChange = (index, field, selectedOption) => {
      setTimeSlots((prev) => {
        const newTimeSlots = [...prev];
        newTimeSlots[index] = {
          ...newTimeSlots[index],
          [field]: selectedOption ? selectedOption.value : "",
        };
        return newTimeSlots;
      });
    };

    // Check for affected events when changing hours
    const checkForAffectedEvents = async (formData) => {
      try {
        setIsCheckingAffectedEvents(true);

        // Call API to check for affected events
        const response = await sdk.callRawAPI(
          "/v3/api/custom/courtmatchup/club/courts/affected-reservations",
          {
            times: formData.times,
            days_off: formData.days_off,
          },
          "POST"
        );

        if (response && !response.error && response.total_affected > 0) {
          setAffectedEventCount(response.total_affected);
          setAffectedReservations(response.affected_reservations || []);
          setPendingData(formData);
          setShowConfirmModal(true);
          return true; // There are affected events
        }

        return false; // No affected events
      } catch (error) {
        console.error("Error checking for affected events:", error);
        return false;
      } finally {
        setIsCheckingAffectedEvents(false);
      }
    };

    // Handle confirmation to proceed with changes
    const handleConfirmChanges = async () => {
      setIsSubmitting(true);
      try {
        // Add flag to delete affected events when user confirms
        const dataWithDeleteFlag = {
          ...pendingData,
          delete_affected_events: true,
        };

        // Now actually save the changes to the profile edit API
        await onSubmit(dataWithDeleteFlag);
        setShowConfirmModal(false);
        setPendingData(null);
      } catch (error) {
        console.error("Error saving changes:", error);
      } finally {
        setIsSubmitting(false);
      }
    };

    // Handle canceling the changes
    const handleCancelChanges = () => {
      setShowConfirmModal(false);
      setPendingData(null);
      setAffectedEventCount(0);
      setAffectedReservations([]);
    };

    React.useImperativeHandle(ref, () => ({
      submit: async () => {
        return new Promise((resolve) => {
          handleSubmit(async (data) => {
            // Transform days off into array format
            const daysOff = Object.entries(formData.daysOff)
              .filter(([_, isOff]) => isOff)
              .map(([day]) => day);

            // Create daily breaks array
            const dailyBreaks = formData.dailyBreaks
              ? [
                  {
                    start: formData.breakStartTime,
                    end: formData.breakEndTime,
                  },
                ]
              : [];

            // Construct the final payload
            const finalData = {
              times: timeSlots,
              daily_breaks: dailyBreaks,
              days_off: daysOff,
            };

            // Always check for affected events first
            const hasAffectedEvents = await checkForAffectedEvents(finalData);

            if (!hasAffectedEvents) {
              // If no affected events, proceed with submission immediately
              await onSubmit(finalData);
            }
            // If there are affected events, the modal will be shown and user must confirm
            // The actual save will happen in handleConfirmChanges

            resolve(finalData);
          })();
        });
      },
    }));
    // const payloadSample = {
    //   daily_breaks: [
    //     {
    //       start: "09:00:00",
    //       end: "10:00:00",
    //     },
    //   ],
    //   days_off: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
    // };
    const [formData, setFormData] = useState({
      times: timeSlots,
      daysOff: {
        Monday: false,
        Tuesday: false,
        Wednesday: false,
        Thursday: false,
        Friday: false,
        Saturday: false,
        Sunday: false,
        ...(club?.days_off
          ? JSON.parse(club.days_off).reduce(
              (acc, day) => ({
                ...acc,
                [day]: true,
              }),
              {}
            )
          : {}),
      },
      dailyBreaks: false,
      breakStartTime: "09:00:00",
      breakEndTime: "10:00:00",
      ...initialData,
    });

    // Update the initial values after state creation
    React.useEffect(() => {
      if (club?.daily_breaks) {
        try {
          const breaks = JSON.parse(club.daily_breaks);
          if (breaks && breaks.length > 0) {
            setFormData((prev) => ({
              ...prev,
              dailyBreaks: true,
              breakStartTime: breaks[0].start || prev.breakStartTime,
              breakEndTime: breaks[0].end || prev.breakEndTime,
            }));
          }
        } catch (error) {
          console.error("Error parsing daily breaks:", error);
        }
      }
    }, [club]);

    const handleDayToggle = (day) => {
      setFormData((prev) => ({
        ...prev,
        daysOff: {
          ...prev.daysOff,
          [day]: !prev.daysOff[day],
        },
      }));
    };

    // Generate time options with seconds for compatibility with club data
    const timeOptions = generateTimeOptions().map((option) => ({
      ...option,
      value: option.value + ":00", // Add seconds to match the HH:MM:SS format in club data
    }));
    console.log("Time options:", timeOptions);

    const handleAddAnotherTime = () => {
      setTimeSlots((prev) => [...prev, { from: "", until: "" }]);
    };

    const handleRemoveTimeSlot = (index) => {
      setTimeSlots((prev) => prev.filter((_, i) => i !== index));
    };
    return (
      <>
        <RightSideModal
          isOpen={isOpen}
          onClose={onClose}
          title={title}
          onPrimaryAction={onPrimaryAction}
          submitting={submitting || isCheckingAffectedEvents}
        >
          <div className="flex flex-col gap-6">
            <div>
              <h3 className="mb-4 text-sm font-medium text-gray-500">
                General opening hours
              </h3>
              {timeSlots.map((timeSlot, index) => (
                <div key={index} className="mb-5 flex items-center gap-4">
                  <div className="flex flex-1 gap-4">
                    <div className="flex-1">
                      <Select
                        classNamePrefix="select"
                        // styles={selectStyles}
                        options={timeOptions}
                        components={{
                          DropdownIndicator: () => null,
                          IndicatorSeparator: () => null,
                        }}
                        value={(() => {
                          const foundOption = timeOptions.find(
                            (option) => option.value === timeSlot.from
                          );
                          console.log(
                            `Finding option for from=${timeSlot.from}:`,
                            foundOption
                          );
                          return foundOption || null;
                        })()}
                        onChange={(option) =>
                          handleTimeChange(index, "from", option)
                        }
                        placeholder="Select time"
                        isClearable
                      />
                    </div>

                    <div className="flex-1">
                      <Select
                        classNamePrefix="select"
                        components={{
                          DropdownIndicator: () => null,
                          IndicatorSeparator: () => null,
                        }}
                        // styles={selectStyles}
                        options={
                          timeSlot.from
                            ? timeOptions.filter(
                                (option) => option.value > timeSlot.from
                              )
                            : timeOptions
                        }
                        value={(() => {
                          const foundOption = timeOptions.find(
                            (option) => option.value === timeSlot.until
                          );
                          console.log(
                            `Finding option for until=${timeSlot.until}:`,
                            foundOption
                          );
                          return foundOption || null;
                        })()}
                        onChange={(option) =>
                          handleTimeChange(index, "until", option)
                        }
                        placeholder="Select time"
                        isDisabled={!timeSlot.from}
                        isClearable
                      />
                    </div>
                  </div>
                  {timeSlots.length > 1 && (
                    <button
                      onClick={() => handleRemoveTimeSlot(index)}
                      className=" text-red-500 hover:text-red-700"
                    >
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M4.79167 4.79102V16.8743C4.79167 17.3346 5.16476 17.7077 5.625 17.7077H14.375C14.8352 17.7077 15.2083 17.3346 15.2083 16.8743V4.79102M4.79167 4.79102H15.2083M4.79167 4.79102H3.125M15.2083 4.79102H16.875M11.6667 8.95768V13.541M8.33333 8.95768V13.541M7.5 4.79102C7.5 3.4103 8.61929 2.29102 10 2.29102C11.3807 2.29102 12.5 3.4103 12.5 4.79102"
                          stroke="#868C98"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </button>
                  )}
                </div>
              ))}
            </div>
            <button
              onClick={handleAddAnotherTime}
              className="mb-5 text-primaryBlue underline hover:text-primaryBlue/80"
            >
              +Add another time slot
            </button>
            <div>
              <h3 className="mb-4 text-sm font-medium text-gray-500">
                Days off
              </h3>
              <div className="grid grid-cols-2 gap-4">
                {Object.keys(formData.daysOff).map((day) => (
                  <div key={day} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={formData.daysOff[day]}
                      onChange={() => handleDayToggle(day)}
                      className="h-4 w-4 rounded border-gray-300"
                    />
                    <span className="text-sm text-gray-600">{day}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* <div>
          <div className="mb-4 flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-500">Daily breaks</h3>
            <button
              onClick={() =>
                setFormData((prev) => ({
                  ...prev,
                  dailyBreaks: !prev.dailyBreaks,
                }))
              }
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                formData.dailyBreaks ? "bg-primaryBlue" : "bg-gray-200"
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  formData.dailyBreaks ? "translate-x-6" : "translate-x-1"
                }`}
              />
            </button>
          </div>
          {formData.dailyBreaks && (
            <div className="mt-1 flex w-full items-start gap-4">
              <div className="flex h-10 min-h-[40px] flex-1 shrink basis-0 items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white shadow-sm">
                <div className="gap-2 self-stretch whitespace-nowrap bg-slate-50 px-2 py-2.5 text-neutral-400">
                  From
                </div>
                <div className="flex-1">
                  <Select
                    className="border-none"
                    classNamePrefix="select"
                    styles={selectStyles}
                    options={timeOptions}
                    value={timeOptions.find(option => option.value === formData.breakStartTime) || null}
                    onChange={(option) =>
                      setFormData((prev) => ({
                        ...prev,
                        breakStartTime: option ? option.value : "",
                      }))
                    }
                    placeholder="Select time"
                    isClearable
                  />
                </div>
              </div>
              <div className="flex h-10 min-h-[40px] flex-1 shrink basis-0 items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white shadow-sm">
                <div className="gap-2 self-stretch whitespace-nowrap bg-slate-50 px-2 py-2.5 text-neutral-400">
                  Until
                </div>
                <div className="flex-1">
                  <Select
                    className="border-none"
                    classNamePrefix="select"
                    styles={selectStyles}
                    options={timeOptions}
                    value={timeOptions.find(option => option.value === formData.breakEndTime) || null}
                    onChange={(option) =>
                      setFormData((prev) => ({
                        ...prev,
                        breakEndTime: option ? option.value : "",
                      }))
                    }
                    placeholder="Select time"
                    isClearable
                  />
                </div>
              </div>
            </div>
          )}
        </div> */}
          </div>
        </RightSideModal>

        {/* Confirmation Modal - Outside of RightSideModal */}
        <ConfirmEventsDeleteModal
          isOpen={showConfirmModal}
          onClose={handleCancelChanges}
          onConfirm={handleConfirmChanges}
          eventCount={affectedEventCount}
          affectedReservations={affectedReservations}
          isSubmitting={isSubmitting}
          type="hours"
        />
      </>
    );
  }
);

ClubSettingsEditForm.displayName = "ClubSettingsEditForm";

export default ClubSettingsEditForm;
