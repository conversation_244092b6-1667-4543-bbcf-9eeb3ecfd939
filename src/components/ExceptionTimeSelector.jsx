import { useState, useEffect, useRef, useContext } from "react";
import BottomDrawer from "./Drawers/BottomDrawer";
import ConfirmEventsDeleteModal from "./Modals/ConfirmEventsDeleteModal";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext } from "Context/Global";
export default function ExceptionTimeSelector({
  showExceptionTimeSelector,
  setShowExceptionTimeSelector,
  selectedException,
  setSelectedExceptionTimes,
  selectedExceptionTimes,
  exceptions,
  onSelectException,
  onSave,
  isSubmitting,
}) {
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const sdk = new MkdSDK();
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState(null);
  const [dragEnd, setDragEnd] = useState(null);
  const dragTimeoutRef = useRef(null);
  const [isEditingName, setIsEditingName] = useState(false);
  const [exceptionName, setExceptionName] = useState(
    selectedException?.name || ""
  );
  const nameInputRef = useRef(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [affectedEventCount, setAffectedEventCount] = useState(0);
  const [affectedReservations, setAffectedReservations] = useState([]);
  const [isCheckingAffectedEvents, setIsCheckingAffectedEvents] =
    useState(false);
  const [pendingExceptions, setPendingExceptions] = useState(null);

  useEffect(() => {
    setExceptionName(selectedException?.name || "");
  }, [selectedException]);

  useEffect(() => {
    if (isEditingName && nameInputRef.current) {
      nameInputRef.current.focus();
    }
  }, [isEditingName]);

  const [currentExceptionIndex, setCurrentExceptionIndex] = useState(() => {
    return (
      exceptions?.findIndex((e) => e.name === selectedException?.name) || 0
    );
  });

  const handlePrevious = () => {
    if (currentExceptionIndex > 0) {
      const newIndex = currentExceptionIndex - 1;
      setCurrentExceptionIndex(newIndex);
      onSelectException(exceptions[newIndex]);
    }
  };

  const handleNext = () => {
    if (currentExceptionIndex < exceptions.length - 1) {
      const newIndex = currentExceptionIndex + 1;
      setCurrentExceptionIndex(newIndex);
      onSelectException(exceptions[newIndex]);
    }
  };

  const days = [
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday",
  ];

  // Generate time slots from 8 AM to 10 PM with 30-minute intervals
  const generateTimeSlots = () => {
    const slots = [];
    for (let hour = 8; hour <= 22; hour++) {
      // Add both :00 and :30 slots for each hour
      slots.push(`${hour.toString().padStart(2, "0")}:00:00`);
      slots.push(`${hour.toString().padStart(2, "0")}:30:00`);
    }
    return slots;
  };

  const timeSlots = generateTimeSlots();

  const handleTimeSelect = (time, day) => {
    const isAlreadySelected = selectedExceptionTimes.some(
      (slot) => slot.time === time && slot.day === day
    );

    if (isAlreadySelected) {
      // If already selected, remove it (deselect)
      setSelectedExceptionTimes((prev) =>
        prev.filter((slot) => !(slot.time === time && slot.day === day))
      );
    } else {
      // If not selected, add it
      setSelectedExceptionTimes((prev) => [...prev, { time, day }]);
    }
  };

  const handleEditTime = (time, day) => {
    // Add logic to edit time slot
    console.log("Edit time:", time, "on", day);
    setActiveDropdown(null);
  };

  const handleDeleteTime = (time, day) => {
    // Add logic to delete time slot
    setSelectedExceptionTimes((prev) =>
      prev.filter((slot) => !(slot.time === time && slot.day === day))
    );
    setActiveDropdown(null);
  };

  // Update the formatSelectedTimes function
  const formatSelectedTimes = () => {
    // Group times by day and format according to the required structure
    const groupedByDay = days
      .map((day) => {
        const dayTimeslots = selectedExceptionTimes
          .filter((slot) => slot.day === day)
          .map((slot) => slot.time)
          .sort();

        // Only include days that have timeslots
        if (dayTimeslots.length > 0) {
          return {
            day: day.toLowerCase(),
            timeslots: dayTimeslots,
          };
        }
        return null;
      })
      .filter(Boolean); // Remove null entries

    return {
      name: selectedException?.name,
      days: groupedByDay,
    };
  };

  // Add function to format time for display
  const formatTimeForDisplay = (time) => {
    const [hour, minute] = time.split(":").map(Number);
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    const period = hour < 12 ? "AM" : "PM";
    const displayMinute = minute.toString().padStart(2, "0");
    return `${displayHour}:${displayMinute} ${period}`;
  };

  const handleNameChange = (e) => {
    setExceptionName(e.target.value);
  };

  const handleNameSubmit = () => {
    if (exceptionName.trim()) {
      selectedException.name = exceptionName.trim();
      setIsEditingName(false);
    }
  };

  const handleNameKeyDown = (e) => {
    if (e.key === "Enter") {
      handleNameSubmit();
    } else if (e.key === "Escape") {
      setExceptionName(selectedException?.name || "");
      setIsEditingName(false);
    }
  };

  // Check for affected events when changing exceptions
  const checkForAffectedEvents = async (updatedExceptions) => {
    try {
      setIsCheckingAffectedEvents(true);

      // Call API to check for affected events
      const response = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/club/courts/affected-reservations",
        {
          exceptions: updatedExceptions,
        },
        "POST"
      );

      if (response && !response.error && response.total_affected > 0) {
        setAffectedEventCount(response.total_affected);
        setAffectedReservations(response.affected_reservations || []);
        setPendingExceptions(updatedExceptions);
        setShowConfirmModal(true);
        return true; // There are affected events
      }

      return false; // No affected events
    } catch (error) {
      console.error("Error checking for affected events:", error);
      return false;
    } finally {
      setIsCheckingAffectedEvents(false);
    }
  };

  // Handle confirmation to proceed with changes
  const handleConfirmChanges = async () => {
    try {
      // Add flag to delete affected events when user confirms
      const exceptionsWithDeleteFlag = {
        exceptions: pendingExceptions,
        delete_affected_events: true,
      };

      // Now actually save the changes to the profile edit API
      await onSave(exceptionsWithDeleteFlag);
      setShowConfirmModal(false);
      setPendingExceptions(null);
      setShowExceptionTimeSelector(false);
      setSelectedExceptionTimes([]);
    } catch (error) {
      console.error("Error saving changes:", error);
    }
  };

  // Handle canceling the changes
  const handleCancelChanges = () => {
    setShowConfirmModal(false);
    setPendingExceptions(null);
    setAffectedEventCount(0);
    setAffectedReservations([]);
  };

  const leftElement = (
    <div className="flex items-center gap-3">
      <div className="flex gap-3">
        <button
          className="rounded-lg bg-[#F6F8FA] p-2"
          onClick={handlePrevious}
          disabled={currentExceptionIndex === 0}
        >
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M3.33268 12.5L9.41008 6.42259C9.73552 6.09715 10.2632 6.09715 10.5886 6.42259L16.666 12.5"
              stroke={currentExceptionIndex === 0 ? "#D1D5DB" : "#868C98"}
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
        <button
          className="rounded-lg bg-[#F6F8FA] p-2"
          onClick={handleNext}
          disabled={currentExceptionIndex === exceptions?.length - 1}
        >
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M16.6673 7.5L10.5899 13.5774C10.2645 13.9028 9.73685 13.9028 9.41141 13.5774L3.33398 7.5"
              stroke={
                currentExceptionIndex === exceptions?.length - 1
                  ? "#D1D5DB"
                  : "#868C98"
              }
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
      </div>
      <div className="flex items-center gap-2">
        {isEditingName ? (
          <input
            ref={nameInputRef}
            type="text"
            value={exceptionName}
            onChange={handleNameChange}
            onBlur={handleNameSubmit}
            onKeyDown={handleNameKeyDown}
            className="w-48 rounded-lg border border-primaryBlue bg-white px-3 py-1 text-xl outline-none"
            placeholder="Enter exception name"
          />
        ) : (
          <div className="flex items-center gap-2">
            <p className="text-xl">{exceptionName}</p>
            <button
              onClick={() => setIsEditingName(true)}
              className="rounded-lg p-1 hover:bg-gray-100"
            >
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M11.7167 7.51667L12.4833 8.28333L4.93333 15.8333H4.16667V15.0667L11.7167 7.51667ZM14.7167 2.5C14.5083 2.5 14.2917 2.58333 14.1333 2.74167L12.6083 4.26667L15.7333 7.39167L17.2583 5.86667C17.5833 5.54167 17.5833 5.01667 17.2583 4.69167L15.3083 2.74167C15.1417 2.575 14.9333 2.5 14.7167 2.5ZM11.7167 5.15833L2.5 14.375V17.5H5.625L14.8417 8.28333L11.7167 5.15833Z"
                  fill="#868C98"
                />
              </svg>
            </button>
          </div>
        )}
      </div>
    </div>
  );

  // Add useEffect to handle initial selected times when selectedException changes
  useEffect(() => {
    if (selectedException) {
      const existingTimes =
        selectedException.days?.flatMap((day) =>
          day.timeslots.map((time) => ({
            day: day.day.charAt(0).toUpperCase() + day.day.slice(1),
            time: time,
          }))
        ) || [];
      setSelectedExceptionTimes(existingTimes);
    }
  }, [selectedException]);

  const isTimeInDragRange = (time, day) => {
    if (!dragStart || !dragEnd) return false;

    const [startDay, startTime] = dragStart.split("-");
    const [endDay, endTime] = dragEnd.split("-");
    const dayIndex = days.indexOf(day);
    const startDayIndex = days.indexOf(startDay);
    const endDayIndex = days.indexOf(endDay);
    const minDayIndex = Math.min(startDayIndex, endDayIndex);
    const maxDayIndex = Math.max(startDayIndex, endDayIndex);

    if (dayIndex < minDayIndex || dayIndex > maxDayIndex) return false;

    const timeIndex = timeSlots.indexOf(time);
    const startTimeIndex = timeSlots.indexOf(startTime);
    const endTimeIndex = timeSlots.indexOf(endTime);
    const minTimeIndex = Math.min(startTimeIndex, endTimeIndex);
    const maxTimeIndex = Math.max(startTimeIndex, endTimeIndex);

    return timeIndex >= minTimeIndex && timeIndex <= maxTimeIndex;
  };

  const handleMouseDown = (time, day) => {
    // Set a small timeout to determine if it's a click or a drag
    if (dragTimeoutRef.current) {
      clearTimeout(dragTimeoutRef.current);
    }

    // Store the initial position
    setDragStart(`${day}-${time}`);
    setDragEnd(`${day}-${time}`);

    // Use a timeout to differentiate between click and drag
    dragTimeoutRef.current = setTimeout(() => {
      setIsDragging(true);
    }, 150); // 150ms is a good threshold for distinguishing click from drag
  };

  const handleMouseEnter = (time, day) => {
    if (isDragging) {
      setDragEnd(`${day}-${time}`);
    }
  };

  const handleMouseUp = () => {
    // Clear the timeout to prevent setting isDragging to true after mouseup
    if (dragTimeoutRef.current) {
      clearTimeout(dragTimeoutRef.current);
      dragTimeoutRef.current = null;
    }

    if (isDragging) {
      const [startDay, startTime] = dragStart.split("-");
      const [endDay, endTime] = dragEnd.split("-");
      const startDayIndex = days.indexOf(startDay);
      const endDayIndex = days.indexOf(endDay);
      const minDayIndex = Math.min(startDayIndex, endDayIndex);
      const maxDayIndex = Math.max(startDayIndex, endDayIndex);

      const startTimeIndex = timeSlots.indexOf(startTime);
      const endTimeIndex = timeSlots.indexOf(endTime);
      const minTimeIndex = Math.min(startTimeIndex, endTimeIndex);
      const maxTimeIndex = Math.max(startTimeIndex, endTimeIndex);

      const newSelectedTimes = [];
      for (let dayIndex = minDayIndex; dayIndex <= maxDayIndex; dayIndex++) {
        const currentDay = days[dayIndex];
        for (
          let timeIndex = minTimeIndex;
          timeIndex <= maxTimeIndex;
          timeIndex++
        ) {
          const currentTime = timeSlots[timeIndex];
          if (
            !selectedExceptionTimes.some(
              (slot) => slot.time === currentTime && slot.day === currentDay
            )
          ) {
            newSelectedTimes.push({ time: currentTime, day: currentDay });
          }
        }
      }

      setSelectedExceptionTimes((prev) => [...prev, ...newSelectedTimes]);
      setIsDragging(false);
      setDragStart(null);
      setDragEnd(null);
    }
  };

  useEffect(() => {
    const handleGlobalMouseUp = () => {
      if (isDragging) {
        handleMouseUp();
      }
    };

    window.addEventListener("mouseup", handleGlobalMouseUp);
    return () => window.removeEventListener("mouseup", handleGlobalMouseUp);
  }, [isDragging, dragStart, dragEnd]);

  return (
    <BottomDrawer
      isOpen={showExceptionTimeSelector}
      onClose={() => setShowExceptionTimeSelector(false)}
      title="Exception times"
      onDiscard={() => {
        setSelectedExceptionTimes([]);
        setShowExceptionTimeSelector(false);
      }}
      discardLabel="Discard"
      showActions
      saveLabel="Save changes"
      leftElement={leftElement}
      onSave={async () => {
        const formattedData = formatSelectedTimes();
        // Update the exceptions array with the new data
        const updatedExceptions = exceptions.map((exception) =>
          exception.name === selectedException.name ? formattedData : exception
        );

        // Always check for affected events first
        const hasAffectedEvents = await checkForAffectedEvents(
          updatedExceptions
        );

        if (!hasAffectedEvents) {
          // If no affected events, proceed with submission immediately
          await onSave(updatedExceptions);
          setShowExceptionTimeSelector(false);
          setSelectedExceptionTimes([]);
        }
        // If there are affected events, the modal will be shown and user must confirm
        // The actual save will happen in handleConfirmChanges
      }}
      isSubmitting={isSubmitting || isCheckingAffectedEvents}
    >
      <div className="w-full">
        <div className="grid grid-cols-7 gap-5">
          {days.map((day) => (
            <div key={day} className="rounded-md bg-white p-2 text-center">
              <div className="mb-2 rounded-md bg-[#F6F8FA] px-3 py-2 font-medium">
                {day}
              </div>
              <div className="space-y-2">
                {timeSlots.map((time) => {
                  const isSelected = selectedExceptionTimes?.some(
                    (slot) => slot.time === time && slot.day === day
                  );
                  const isInDragRange =
                    isDragging && isTimeInDragRange(time, day);
                  const dropdownId = `${day}-${time}`;

                  return (
                    <div key={`${day}-${time}`} className="relative">
                      <button
                        className={`w-full rounded-md border-2 border-gray-100 px-3 py-2 text-sm font-medium text-gray-500 ${
                          isSelected
                            ? "border-2 border-primaryBlue bg-[#EBF1FF] text-primaryBlue"
                            : isInDragRange
                            ? "border-primaryBlue bg-[#EBF1FF] bg-opacity-50"
                            : "border-gray-300 hover:border-gray-400"
                        }`}
                        onMouseDown={() => handleMouseDown(time, day)}
                        onMouseEnter={() => handleMouseEnter(time, day)}
                        onClick={() => {
                          // Clear the drag timeout to prevent it from becoming a drag
                          if (dragTimeoutRef.current) {
                            clearTimeout(dragTimeoutRef.current);
                            dragTimeoutRef.current = null;
                          }

                          // If we're not dragging, treat it as a click
                          if (!isDragging) {
                            handleTimeSelect(time, day);
                          }

                          // Reset drag state
                          setIsDragging(false);
                          setDragStart(null);
                          setDragEnd(null);
                        }}
                      >
                        {formatTimeForDisplay(time)}
                        {/* {isSelected && (
                          <button
                            className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                            onClick={(e) => {
                              e.stopPropagation();
                              setActiveDropdown(
                                activeDropdown === dropdownId
                                  ? null
                                  : dropdownId
                              );
                            }}
                          >
                            <svg
                              className="h-4 w-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                              />
                            </svg>
                          </button>
                        )} */}
                      </button>

                      {activeDropdown === dropdownId && (
                        <div className="absolute right-0 z-10 mt-1 w-36 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5">
                          <div className="py-1">
                            {/* <button
                              onClick={() => handleEditTime(time, day)}
                              className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            >
                              <svg
                                className="mr-3 h-4 w-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                                />
                              </svg>
                              Edit
                            </button> */}
                            <button
                              onClick={() => handleDeleteTime(time, day)}
                              className="flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                            >
                              <svg
                                className="mr-3 h-4 w-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                />
                              </svg>
                              Delete
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        {/* Confirmation Modal */}
        <ConfirmEventsDeleteModal
          isOpen={showConfirmModal}
          onClose={handleCancelChanges}
          onConfirm={handleConfirmChanges}
          eventCount={affectedEventCount}
          affectedReservations={affectedReservations}
          isSubmitting={isSubmitting}
          type="exception"
        />
      </div>
    </BottomDrawer>
  );
}
