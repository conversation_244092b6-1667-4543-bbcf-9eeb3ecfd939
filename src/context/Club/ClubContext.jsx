import React, {
  createContext,
  useContext,
  useReducer,
  useEffect,
  useCallback,
  useMemo,
} from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../Auth/AuthContext";
import LoadingSpinner from "Components/LoadingSpinner";
import { clubReducer, initialState, CLUB_ACTIONS } from "./clubReducer";
import {
  fetchUserSubscription,
  fetchProfileByRole,
  fetchClubProfile,
  fetchUserClubData,
  createUserPermissions,
} from "./clubServices";
import { USER_ROLES } from "./clubConstants";

/**
 * Club Context for managing club-related state and operations
 */
const ClubContext = createContext();

/**
 * Club Provider Component
 * Manages club-related state and provides context to child components
 */

export const ClubProvider = ({ children }) => {
  const [state, dispatch] = useReducer(clubReducer, initialState);
  const { state: authState } = useAuth();
  const navigate = useNavigate();

  // Memoized values to prevent unnecessary re-renders
  const userRole = useMemo(() => localStorage.getItem("role"), []);
  const userId = useMemo(() => localStorage.getItem("user"), []);

  /**
   * Sets user permissions based on subscription and membership settings
   * @param {Object} subscription - User subscription data
   * @param {Array} membershipSettings - Club membership settings
   */
  const setUserPermissions = useCallback((subscription, membershipSettings) => {
    if (!subscription || !membershipSettings) return;

    try {
      const permissions = createUserPermissions(
        subscription,
        membershipSettings
      );
      if (permissions) {
        dispatch({
          type: CLUB_ACTIONS.SET_USER_PERMISSIONS,
          payload: permissions,
        });
      }
    } catch (error) {
      console.error("Error setting user permissions:", error);
      dispatch({
        type: CLUB_ACTIONS.SET_ERROR,
        payload: "Error setting user permissions",
      });
    }
  }, []);

  /**
   * Fetches club data based on user role
   * Optimized with better error handling and modular approach
   */
  const fetchClubData = useCallback(async () => {
    if (!userId || !authState.isAuthenticated) return;

    dispatch({ type: CLUB_ACTIONS.SET_LOADING, payload: true });

    try {
      let clubId;
      let userSubscription;

      // Step 1: Fetch user subscription if user role
      if (userRole === USER_ROLES.USER) {
        try {
          const { subscription, error } = await fetchUserSubscription(userId);

          if (error) {
            throw new Error(error);
          }

          userSubscription = subscription;
          dispatch({
            type: CLUB_ACTIONS.SET_USER_SUBSCRIPTION,
            payload: subscription,
          });
        } catch (error) {
          console.error("Error fetching user subscription:", error);
          dispatch({
            type: CLUB_ACTIONS.SET_ERROR,
            payload: error.message,
          });
        }
      }

      // Step 2: Fetch profile data based on role
      try {
        const { profileData, clubIdFromProfile, error } =
          await fetchProfileByRole(userRole, userId);

        if (error) {
          throw new Error(error);
        }

        clubId = clubIdFromProfile;

        // Set appropriate profile data based on role
        if (profileData) {
          const actionType = {
            [USER_ROLES.USER]: CLUB_ACTIONS.SET_USER_PROFILE,
            [USER_ROLES.COACH]: CLUB_ACTIONS.SET_COACH_PROFILE,
            [USER_ROLES.STAFF]: CLUB_ACTIONS.SET_STAFF_PROFILE,
          }[userRole];

          if (actionType) {
            dispatch({ type: actionType, payload: profileData });
          }

          // Set staff permissions if applicable
          if (userRole === USER_ROLES.STAFF && profileData.permissions) {
            dispatch({
              type: CLUB_ACTIONS.SET_CLUB_PERMISSIONS,
              payload: profileData.permissions,
            });
          }
        }
      } catch (error) {
        console.error(`Error fetching ${userRole} profile:`, error);
        dispatch({ type: CLUB_ACTIONS.SET_ERROR, payload: error.message });
      }

      // Step 3: Fetch club data
      try {
        if (userRole === USER_ROLES.CLUB) {
          // For club role, fetch club profile
          const { clubData, error } = await fetchClubProfile();

          if (error) {
            throw new Error(error);
          }

          if (clubData) {
            dispatch({ type: CLUB_ACTIONS.SET_CLUB, payload: clubData.club });
            dispatch({
              type: CLUB_ACTIONS.SET_SPORTS,
              payload: clubData.sports,
            });
            dispatch({
              type: CLUB_ACTIONS.SET_COURTS,
              payload: clubData.courts,
            });
            dispatch({
              type: CLUB_ACTIONS.SET_PRICING,
              payload: clubData.pricing,
            });
          }
        } else if (clubId) {
          // For other roles, fetch club data by ID
          const { clubData, membershipSettings, error } =
            await fetchUserClubData(clubId);

          if (error) {
            throw new Error(error);
          }

          if (clubData) {
            dispatch({ type: CLUB_ACTIONS.SET_CLUB, payload: clubData.model });
            dispatch({
              type: CLUB_ACTIONS.SET_SPORTS,
              payload: clubData.sports,
            });
            dispatch({
              type: CLUB_ACTIONS.SET_COURTS,
              payload: clubData.courts,
            });
            dispatch({
              type: CLUB_ACTIONS.SET_PRICING,
              payload: clubData.pricing,
            });

            // Set membership settings
            dispatch({
              type: CLUB_ACTIONS.SET_CLUB_MEMBERSHIP,
              payload: membershipSettings,
            });

            // Set user permissions if applicable
            if (userRole === USER_ROLES.USER && userSubscription) {
              setUserPermissions(userSubscription, membershipSettings);

              // Check if user needs to be redirected to buy membership
              if (
                userSubscription.subId === null &&
                userSubscription.planId === null &&
                membershipSettings.length >= 1
              ) {
                navigate("/user/membership/buy");
              }
            }
          }
        }
      } catch (error) {
        console.error("Error fetching club data:", error);
        dispatch({ type: CLUB_ACTIONS.SET_ERROR, payload: error.message });
      }

      dispatch({ type: CLUB_ACTIONS.SET_LOADING, payload: false });
    } catch (error) {
      console.error("Error in fetchClubData:", error);
      dispatch({ type: CLUB_ACTIONS.SET_ERROR, payload: error.message });
      dispatch({ type: CLUB_ACTIONS.SET_LOADING, payload: false });
    }
  }, [
    userId,
    userRole,
    authState.isAuthenticated,
    navigate,
    setUserPermissions,
  ]);

  /**
   * Triggers a refetch of club data
   */
  const triggerRefetch = useCallback(() => {
    dispatch({ type: CLUB_ACTIONS.TRIGGER_REFETCH });
  }, []);

  /**
   * Reset all state values to their defaults
   */
  const resetState = useCallback(() => {
    dispatch({ type: CLUB_ACTIONS.SET_CLUB, payload: null });
    dispatch({ type: CLUB_ACTIONS.SET_SPORTS, payload: [] });
    dispatch({ type: CLUB_ACTIONS.SET_PRICING, payload: [] });
    dispatch({ type: CLUB_ACTIONS.SET_COURTS, payload: [] });
    dispatch({ type: CLUB_ACTIONS.SET_COACH_PROFILE, payload: null });
    dispatch({ type: CLUB_ACTIONS.SET_USER_PROFILE, payload: null });
    dispatch({ type: CLUB_ACTIONS.SET_STAFF_PROFILE, payload: null });
    dispatch({ type: CLUB_ACTIONS.SET_CLUB_MEMBERSHIP, payload: [] });
    dispatch({ type: CLUB_ACTIONS.SET_USER_SUBSCRIPTION, payload: null });
    dispatch({ type: CLUB_ACTIONS.SET_USER_PERMISSIONS, payload: null });
    dispatch({ type: CLUB_ACTIONS.SET_CLUB_PERMISSIONS, payload: null });
  }, []);

  // Fetch club data when authenticated or when refetch is triggered
  useEffect(() => {
    if (userId && authState.isAuthenticated) {
      fetchClubData();
    } else {
      resetState();
    }
  }, [
    userId,
    authState.isAuthenticated,
    state.refetchTrigger,
    fetchClubData,
    resetState,
  ]);

  // Memoize context value to prevent unnecessary re-renders
  const value = useMemo(
    () => ({
      ...state,
      fetchClubData,
      setUserPermissions,
      triggerRefetch,
    }),
    [state, fetchClubData, setUserPermissions, triggerRefetch]
  );

  return (
    <ClubContext.Provider value={value}>
      {state.loading && <LoadingSpinner />}
      {children}
    </ClubContext.Provider>
  );
};

/**
 * Custom hook to use the Club context
 * @returns {Object} Club context value
 * @throws {Error} If used outside of ClubProvider
 */
export const useClub = () => {
  const context = useContext(ClubContext);
  if (context === undefined) {
    throw new Error("useClub must be used within a ClubProvider");
  }
  return context;
};

export default ClubContext;
