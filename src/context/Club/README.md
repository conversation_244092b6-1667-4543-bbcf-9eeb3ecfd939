# Club Context Refactoring

## Overview
The ClubContext has been refactored to improve maintainability, performance, and code organization. The large monolithic file has been split into smaller, focused modules.

## File Structure

### 📁 src/context/Club/
- **ClubContext.jsx** - Main context provider and hooks
- **clubReducer.js** - State management reducer and action types
- **clubServices.js** - API service functions
- **clubConstants.js** - Centralized constants and configuration
- **README.md** - This documentation

## Key Improvements

### 🚀 Performance Optimizations
- **Memoized values**: `userRole`, `userId`, and context value are memoized to prevent unnecessary re-renders
- **useCallback hooks**: All functions are wrapped in `useCallback` for better performance
- **Optimized useEffect**: Dependencies are properly managed to prevent infinite loops

### 🏗️ Better Code Organization
- **Separation of concerns**: Each file has a single responsibility
- **Modular architecture**: Easy to test and maintain individual components
- **Centralized constants**: All magic strings and configuration in one place

### 🛡️ Improved Error Handling
- **Structured error responses**: All service functions return `{ data, error }` format
- **Comprehensive error messages**: Centralized error message constants
- **Better error logging**: More descriptive console errors with context

### 📚 Enhanced Documentation
- **JSDoc comments**: All functions have proper documentation
- **Type hints**: Parameter and return types documented
- **Usage examples**: Clear documentation for developers

## API Service Functions

### `fetchUserSubscription(userId)`
Fetches user subscription data from Stripe.

### `fetchProfileByRole(userRole, userId)`
Fetches profile data based on user role (user, coach, staff, club).

### `fetchClubProfile()`
Fetches club profile data for club role users.

### `fetchUserClubData(clubId)`
Fetches club data for non-club role users, including membership settings.

### `createUserPermissions(subscription, membershipSettings)`
Creates user permissions object from subscription and membership data.

## State Management

### Actions
All actions are defined as constants in `CLUB_ACTIONS`:
- `SET_LOADING`
- `SET_CLUB`
- `SET_SPORTS`
- `SET_COURTS`
- `SET_PRICING`
- `SET_*_PROFILE` (for different user types)
- `SET_USER_SUBSCRIPTION`
- `SET_USER_PERMISSIONS`
- `SET_CLUB_PERMISSIONS`
- `SET_CLUB_MEMBERSHIP`
- `SET_ERROR`
- `TRIGGER_REFETCH`

### State Structure
```javascript
{
  club: null,
  sports: [],
  courts: [],
  coach_profile: null,
  user_profile: null,
  staff_profile: null,
  staff_access: null,
  club_membership: [],
  user_subscription: null,
  user_permissions: null,
  loading: false,
  error: null,
  pricing: [],
  club_permissions: null,
  refetchTrigger: 0,
}
```

## Usage

### Basic Usage
```javascript
import { useClub } from 'context/Club/ClubContext';

function MyComponent() {
  const { 
    club, 
    sports, 
    loading, 
    error, 
    fetchClubData, 
    triggerRefetch 
  } = useClub();

  // Use the context data...
}
```

### Provider Setup
```javascript
import { ClubProvider } from 'context/Club/ClubContext';

function App() {
  return (
    <ClubProvider>
      <YourAppComponents />
    </ClubProvider>
  );
}
```

## Migration Notes

### Breaking Changes
- None - the public API remains the same

### Benefits
- ✅ Better performance with memoization
- ✅ Improved error handling
- ✅ Better code organization
- ✅ Enhanced debugging with better logging
- ✅ Easier testing with modular functions
- ✅ Better TypeScript support (if migrated later)

## Fixed Issues

### Membership Settings Parsing
- ✅ Robust parsing that handles both string and array formats
- ✅ Proper error handling for malformed JSON
- ✅ Detailed logging for debugging
- ✅ Fallback to empty array on parse errors

### Performance Issues
- ✅ Eliminated unnecessary re-renders
- ✅ Optimized useEffect dependencies
- ✅ Memoized expensive computations

### Code Maintainability
- ✅ Separated concerns into focused modules
- ✅ Centralized constants and configuration
- ✅ Improved error handling patterns
- ✅ Better documentation and comments
