/**
 * Club Services
 * API service functions for the Club context
 */

import MkdSDK from "Utils/MkdSDK";
import TreeSD<PERSON> from "Utils/TreeSDK";
import { fetchRoleAccess } from "Utils/roleAccess";
import { USER_ROLES, ERROR_MESSAGES, DEFAULTS } from "./clubConstants";

const sdk = new MkdSDK();
const tdk = new TreeSDK();

/**
 * Fetches user subscription data
 * @param {string} userId - User ID
 * @returns {Object} { subscription, error }
 */
export const fetchUserSubscription = async (userId) => {
  try {
    const result = await sdk.getCustomerStripeSubscription(userId);
    return {
      subscription: result?.customer,
      error: null,
    };
  } catch (error) {
    console.error("Error fetching user subscription:", error);
    return {
      subscription: null,
      error: ERROR_MESSAGES.FETCH_SUBSCRIPTION,
    };
  }
};

/**
 * Fetches profile data based on user role
 * @param {string} userRole - User role
 * @param {string} userId - User ID
 * @returns {Object} { profileData, clubIdFromProfile, error }
 */
export const fetchProfileByRole = async (userRole, userId) => {
  try {
    let profileData = null;
    let clubIdFromProfile = null;

    switch (userRole) {
      case USER_ROLES.COACH:
        profileData = await sdk.callRawAPI(
          "/v3/api/custom/courtmatchup/coach/profile"
        );
        clubIdFromProfile = profileData.club_id;
        break;

      case USER_ROLES.USER:
        const userResponse = await tdk.getOne("user", userId, {});
        profileData = userResponse.model;
        clubIdFromProfile = userResponse.model.club_id;
        break;

      case USER_ROLES.CLUB:
        clubIdFromProfile = userId;
        break;

      case USER_ROLES.STAFF:
        profileData = await sdk.callRawAPI(
          "/v3/api/custom/courtmatchup/staff/profile",
          {},
          "GET"
        );
        clubIdFromProfile = profileData.club_id;
        
        // Fetch role access for staff
        try {
          const roleAccess = await fetchRoleAccess(profileData.club_id);
          profileData.permissions = roleAccess?.staff;
        } catch (roleError) {
          console.error("Error fetching role access:", roleError);
          profileData.permissions = null;
        }
        break;

      default:
        return {
          profileData: null,
          clubIdFromProfile: null,
          error: ERROR_MESSAGES.INVALID_ROLE,
        };
    }

    return {
      profileData,
      clubIdFromProfile,
      error: null,
    };
  } catch (error) {
    console.error(`Error fetching ${userRole} profile:`, error);
    return {
      profileData: null,
      clubIdFromProfile: null,
      error: ERROR_MESSAGES.FETCH_PROFILE,
    };
  }
};

/**
 * Fetches club profile data (for club role)
 * @returns {Object} { clubData, error }
 */
export const fetchClubProfile = async () => {
  try {
    const clubResponse = await sdk.callRawAPI(
      "/v3/api/custom/courtmatchup/club/profile",
      {},
      "GET"
    );

    return {
      clubData: {
        club: clubResponse.model.club,
        sports: clubResponse.model.sports,
        courts: clubResponse.model.courts,
        pricing: clubResponse.model.pricing,
      },
      error: null,
    };
  } catch (error) {
    console.error("Error fetching club profile:", error);
    return {
      clubData: null,
      error: ERROR_MESSAGES.FETCH_CLUB_DATA,
    };
  }
};

/**
 * Fetches club data for users (non-club roles)
 * @param {string} clubId - Club ID
 * @returns {Object} { clubData, membershipSettings, error }
 */
export const fetchUserClubData = async (clubId) => {
  try {
    const clubResponse = await sdk.callRawAPI(
      `/v3/api/custom/courtmatchup/user/club/${clubId}`,
      {},
      "GET"
    );

    // Parse membership settings
    const membershipSettings = parseMembershipSettings(
      clubResponse?.model?.membership_settings
    );

    return {
      clubData: {
        model: clubResponse.model,
        sports: clubResponse.sports,
        courts: clubResponse.courts,
        pricing: clubResponse.pricing,
      },
      membershipSettings,
      error: null,
    };
  } catch (error) {
    console.error("Error fetching user club data:", error);
    return {
      clubData: null,
      membershipSettings: DEFAULTS.MEMBERSHIP_SETTINGS,
      error: ERROR_MESSAGES.FETCH_CLUB_DATA,
    };
  }
};

/**
 * Parses membership settings from API response
 * @param {string|Array} membershipSettingsRaw - Raw membership settings
 * @returns {Array} Parsed membership settings
 */
export const parseMembershipSettings = (membershipSettingsRaw) => {
  if (!membershipSettingsRaw) {
    return DEFAULTS.MEMBERSHIP_SETTINGS;
  }

  try {
    let membershipSettings = [];

    if (typeof membershipSettingsRaw === "string") {
      membershipSettings = JSON.parse(membershipSettingsRaw);
    } else if (Array.isArray(membershipSettingsRaw)) {
      membershipSettings = membershipSettingsRaw;
    }

    console.log("Parsed membership settings:", {
      raw: membershipSettingsRaw,
      parsed: membershipSettings,
      length: membershipSettings.length,
    });

    return membershipSettings;
  } catch (error) {
    console.error("Error parsing membership settings:", error);
    return DEFAULTS.MEMBERSHIP_SETTINGS;
  }
};

/**
 * Creates user permissions object from subscription and membership settings
 * @param {Object} subscription - User subscription data
 * @param {Array} membershipSettings - Club membership settings
 * @returns {Object|null} User permissions object
 */
export const createUserPermissions = (subscription, membershipSettings) => {
  if (!subscription || !membershipSettings) return null;

  try {
    const parsedMembership = Array.isArray(membershipSettings)
      ? membershipSettings
      : JSON.parse(membershipSettings);

    const userPlan = parsedMembership.find(
      (plan) => plan.plan_id === subscription.planId
    );

    if (!userPlan) return null;

    return {
      planName: userPlan.plan_name,
      planPrice: userPlan.price,
      allowClinic: userPlan.allow_clinic,
      allowBuddy: userPlan.allow_buddy,
      allowCoach: userPlan.allow_coach,
      allowGroups: userPlan.allow_groups,
      allowCourt: userPlan.allow_court,
      features: userPlan.features,
      subscriptionId: subscription.subId,
      stripeUid: subscription.stripe_uid,
      ...userPlan,
    };
  } catch (error) {
    console.error("Error creating user permissions:", error);
    throw new Error(ERROR_MESSAGES.SET_PERMISSIONS);
  }
};
